<template>
  <div class="hanzi-dashboard">
    <!-- 基础数据支撑区域 -->
    <div class="support-data-section">
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :md="6" v-for="card in supportDataCards" :key="card.key">
          <el-card class="support-card" :class="`support-${card.key}`">
            <div class="support-content">
              <div class="support-icon">
                <el-icon :size="24" :color="card.color">
                  <component :is="card.icon" />
                </el-icon>
              </div>
              <div class="support-info">
                <div class="support-title">{{ card.title }}</div>
                <div class="support-value">{{ card.value }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近活动区域 -->
    <div class="activity-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Clock /></el-icon>
            <span>最近校对活动</span>
            <el-button type="text" size="small" @click="refreshActivities">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
        </template>
        <el-timeline class="activity-timeline">
          <el-timeline-item
            v-for="activity in recentActivities"
            :key="activity.id"
            :timestamp="formatTime(activity.timestamp)"
            :type="getActivityType(activity.type)"
          >
            <div class="activity-content">
              <div class="activity-description">{{ activity.description }}</div>
              <div class="activity-meta">
                <span class="activity-hanzi" v-if="activity.hanzi">{{ activity.hanzi }}</span>
                <span class="activity-user">{{ activity.user }}</span>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
        <div v-if="recentActivities.length === 0" class="no-activities">
          <el-empty description="暂无最近活动" :image-size="60" />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import {
  Clock, Refresh
} from '@element-plus/icons-vue'
import { dashboardApi, dashboardUtils } from '@/api/dashboard'
import { ElMessage } from 'element-plus'

// 响应式数据
const loading = ref(false)
const overviewData = ref(null)
const recentActivities = ref([])

// 计算属性
const supportDataCards = computed(() => [
  {
    key: 'hanzi',
    title: '汉字库规模',
    value: overviewData.value?.supporting_data.hanzi_foundation.total_hanzi?.toLocaleString() || '0',
    icon: 'Document',
    color: '#909399'
  },
  {
    key: 'zhengyi',
    title: '正异关系',
    value: overviewData.value?.supporting_data.relation_support.zhengyi_relations?.toLocaleString() || '0',
    icon: 'Connection',
    color: '#909399'
  },
  {
    key: 'fanjian',
    title: '繁简关系',
    value: overviewData.value?.supporting_data.relation_support.fanjian_relations?.toLocaleString() || '0',
    icon: 'Switch',
    color: '#909399'
  }
])

// 方法
const fetchOverviewData = async () => {
  loading.value = true
  try {
    // 获取概览数据
    const overviewResponse = await dashboardApi.getOverview()
    if (overviewResponse.success) {
      overviewData.value = overviewResponse.data
    }

    // 获取活动数据
    const activitiesResponse = await dashboardApi.getActivities(10)
    if (activitiesResponse.success) {
      recentActivities.value = activitiesResponse.data || []
    }
  } catch (error) {
    console.error('获取概览数据失败:', error)
    ElMessage.error('获取数据失败: ' + error.message)
    // 清空数据，显示错误状态
    overviewData.value = null
    recentActivities.value = []
  } finally {
    loading.value = false
  }
}

const refreshActivities = async () => {
  try {
    const response = await dashboardApi.getActivities(10)
    if (response.success) {
      recentActivities.value = response.data || []
      ElMessage.success('活动数据已刷新')
    }
  } catch (error) {
    console.error('刷新活动数据失败:', error)
    ElMessage.error('刷新失败: ' + error.message)
  }
}

const getActivityType = (type) => {
  return dashboardUtils.getActivityType(type)
}

const formatTime = (timestamp) => {
  return dashboardUtils.formatTime(timestamp)
}

// 生命周期
onMounted(() => {
  fetchOverviewData()
})
</script>

<style scoped>
.hanzi-dashboard {
  padding: 24px;
  background-color: #f8fafc;
}

.support-data-section {
  margin-bottom: 24px;
}

.support-card {
  height: 120px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.support-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.support-icon {
  margin-right: 16px;
}

.support-info {
  flex: 1;
}

.support-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
}

.support-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.support-impact {
  font-size: 12px;
  color: #909399;
}

.activity-section .card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header {
  display: flex;
  align-items: center;
}

.card-header .el-icon {
  margin-right: 8px;
}

.activity-timeline {
  max-height: 400px;
  overflow-y: auto;
}

.activity-content {
  padding-left: 8px;
}

.activity-description {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.activity-meta {
  font-size: 12px;
  color: #909399;
}

.activity-hanzi {
  font-family: var(--chinese-font-family);
  font-weight: bold;
  margin-right: 8px;
}

.no-activities {
  text-align: center;
  padding: 40px 0;
}

@media (max-width: 768px) {
  .hanzi-dashboard {
    padding: 16px;
  }
}
</style>
